import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import { BorderRadius, Colors, Shadows, Spacing } from '../constants/theme';
import { formatCurrency } from '../utils/designSystem';

interface ProgressCardProps {
  currentAmount: number;
  targetAmount: number;
  onPress?: () => void;
  style?: any;
}

export const ProgressCard: React.FC<ProgressCardProps> = ({
  currentAmount,
  targetAmount,
  onPress,
  style
}) => {
  const progressPercentage = Math.round((currentAmount / targetAmount) * 100);
  const remainingAmount = targetAmount - currentAmount;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.95}
      accessibilityRole="button"
      accessibilityLabel={`Progress tabungan ${progressPercentage} persen. Terkumpul ${formatCurrency(currentAmount)} dari target ${formatCurrency(targetAmount)}`}
      accessibilityHint="Ketuk untuk melihat detail progress dan riwayat tabungan"
      accessible={true}
    >
      <LinearGradient
        colors={Colors.gradients.card as any}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        {/* Professional Header */}
        <View style={styles.cardHeader}>
          <View style={styles.headerLeft}>
            <Ionicons name="trending-up" size={20} color={Colors.primary} />
            <Text style={styles.cardTitle}>Progress Tabungan</Text>
          </View>
          <View style={styles.headerRight}>
            <Text style={styles.percentageBadge}>{progressPercentage}%</Text>
          </View>
        </View>

        <View style={styles.content}>
          {/* Enhanced Chart Section */}
          <View style={styles.chartSection}>
            <View style={styles.chartContainer}>
              <AnimatedCircularProgress
                size={110}
                width={10}
                fill={progressPercentage}
                tintColor={Colors.primary}
                backgroundColor={Colors.accentMuted}
                rotation={0}
                lineCap="round"
                duration={1200}
              >
                {() => (
                  <View style={styles.chartCenter}>
                    <Text style={styles.percentageText}>{progressPercentage}%</Text>
                    <Text style={styles.percentageLabel}>Tercapai</Text>
                  </View>
                )}
              </AnimatedCircularProgress>
            </View>
          </View>

          {/* Professional Amount Grid */}
          <View style={styles.amountGrid}>
            <View style={styles.amountCard}>
              <View style={styles.amountHeader}>
                <Ionicons name="wallet" size={16} color={Colors.success} />
                <Text style={styles.amountLabel}>Terkumpul</Text>
              </View>
              <Text style={styles.currentAmount}>
                {formatCurrency(currentAmount)}
              </Text>
            </View>

            <View style={styles.amountCard}>
              <View style={styles.amountHeader}>
                <Ionicons name="flag" size={16} color={Colors.primary} />
                <Text style={styles.amountLabel}>Target</Text>
              </View>
              <Text style={styles.targetAmount}>
                {formatCurrency(targetAmount)}
              </Text>
            </View>

            <View style={[styles.amountCard, styles.remainingCard]}>
              <View style={styles.amountHeader}>
                <Ionicons name="hourglass" size={16} color={Colors.warning} />
                <Text style={styles.amountLabel}>Sisa</Text>
              </View>
              <Text style={styles.remainingAmount}>
                {formatCurrency(remainingAmount)}
              </Text>
            </View>
          </View>
        </View>

        {/* Enhanced Progress Bar */}
        <View style={styles.progressSection}>
          <View style={styles.progressBar}>
            <LinearGradient
              colors={Colors.gradients.progress as any}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={[
                styles.progressFill,
                { width: `${Math.min(progressPercentage, 100)}%` }
              ]}
            />
          </View>
          <View style={styles.progressLabels}>
            <Text style={styles.progressStart}>0%</Text>
            <Text style={styles.progressEnd}>100%</Text>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: Spacing.container.horizontal,
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },

  gradient: {
    padding: Spacing.card.padding,
  },

  // Professional Header
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  headerRight: {
    alignItems: 'flex-end',
  },

  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  percentageBadge: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.primary,
    backgroundColor: Colors.accentLight,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },

  content: {
    flexDirection: 'column',
    gap: Spacing.lg,
  },

  // Enhanced Chart Section
  chartSection: {
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  chartCenter: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  percentageText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
  },

  percentageLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginTop: 2,
  },

  // Professional Amount Grid
  amountGrid: {
    flexDirection: 'column',
    gap: Spacing.sm,
  },

  amountCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: BorderRadius.sm,
    padding: Spacing.md,
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary,
  },

  remainingCard: {
    borderLeftColor: Colors.warning,
  },

  amountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    marginBottom: 4,
  },

  amountLabel: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.textSecondary,
  },

  currentAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.success,
  },

  targetAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  remainingAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.warning,
  },

  // Enhanced Progress Section
  progressSection: {
    marginTop: Spacing.lg,
  },

  progressBar: {
    height: 8,
    backgroundColor: Colors.accentMuted,
    borderRadius: BorderRadius.pill,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    borderRadius: BorderRadius.pill,
  },

  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.xs,
  },

  progressStart: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.textTertiary,
  },

  progressEnd: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.textTertiary,
  },
});

export default ProgressCard;
