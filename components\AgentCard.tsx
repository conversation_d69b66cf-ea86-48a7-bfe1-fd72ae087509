import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { BorderRadius, Colors, Shadows, Spacing } from '../constants/theme';


interface AgentCardProps {
  agentName: string;
  agentTitle?: string;
  rating?: number;
  responseTime?: string;
  onPress?: () => void;
  onCallPress?: () => void;
  onChatPress?: () => void;
  style?: any;
}

export const AgentCard: React.FC<AgentCardProps> = ({
  agentName,
  agentTitle = "Agent Terpercaya",
  rating = 4.9,
  responseTime = "< 5 menit",
  onPress,
  onCallPress,
  onChatPress,
  style
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.9}
      accessibilityRole="button"
      accessibilityLabel={`Agent ${agentName}, siap membantu Anda`}
      accessibilityHint="Ketuk untuk menghubungi agent melalui chat"
      accessible={true}
    >
      <LinearGradient
        colors={Colors.gradients.card as any}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        {/* Professional Header */}
        <View style={styles.cardHeader}>
          <View style={styles.headerLeft}>
            <Ionicons name="person-circle" size={18} color={Colors.primary} />
            <Text style={styles.cardTitle}>Agent Anda</Text>
          </View>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={Colors.gold} />
            <Text style={styles.ratingText}>{rating}</Text>
          </View>
        </View>

        <View style={styles.content}>
          {/* Professional Avatar Section */}
          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              <LinearGradient
                colors={[Colors.primary, Colors.primaryLight]}
                style={styles.avatarGradient}
              >
                <Ionicons name="person" size={32} color="#fff" />
              </LinearGradient>

              {/* Professional Status Badge */}
              <View style={styles.statusBadge}>
                <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              </View>
            </View>
          </View>

          {/* Enhanced Info Section */}
          <View style={styles.infoSection}>
            <Text style={styles.agentName}>{agentName}</Text>
            <Text style={styles.agentTitle}>{agentTitle}</Text>

            <View style={styles.statusContainer}>
              <View style={styles.statusDot} />
              <Text style={styles.statusText}>Online</Text>
            </View>

            <View style={styles.responseContainer}>
              <Ionicons name="time" size={12} color={Colors.textSecondary} />
              <Text style={styles.responseText}>Respon {responseTime}</Text>
            </View>
          </View>
        </View>

        {/* Professional Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onChatPress}
            activeOpacity={0.8}
          >
            <Ionicons name="chatbubble-outline" size={18} color={Colors.primary} />
            <Text style={styles.actionText}>Chat</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={onCallPress}
            activeOpacity={0.8}
          >
            <Ionicons name="call" size={18} color="#fff" />
            <Text style={styles.primaryActionText}>Hubungi</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
    borderRadius: BorderRadius.card,
    overflow: 'hidden',
    ...Shadows.card,
  },

  gradient: {
    padding: Spacing.card.padding,
  },

  // Professional Header
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },

  cardTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.textSecondary,
  },

  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  ratingText: {
    fontSize: 13,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    marginBottom: Spacing.md,
  },

  // Professional Avatar
  avatarSection: {
    position: 'relative',
  },

  avatarContainer: {
    position: 'relative',
  },

  avatarGradient: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },

  statusBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: Colors.surface,
    borderRadius: 10,
    padding: 2,
  },

  // Enhanced Info Section
  infoSection: {
    flex: 1,
    gap: 4,
  },

  agentName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },

  agentTitle: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginBottom: 4,
  },

  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 2,
  },

  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.success,
  },

  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.success,
  },

  responseContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },

  responseText: {
    fontSize: 11,
    fontWeight: '400',
    color: Colors.textSecondary,
  },

  // Professional Action Section
  actionSection: {
    flexDirection: 'row',
    gap: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.accentMuted,
  },

  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.accentLight,
  },

  primaryButton: {
    backgroundColor: Colors.primary,
  },

  actionText: {
    fontSize: 13,
    fontWeight: '500',
    color: Colors.primary,
  },

  primaryActionText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#fff',
  },
});

export default AgentCard;
