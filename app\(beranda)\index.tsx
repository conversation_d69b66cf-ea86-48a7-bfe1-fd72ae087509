import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import React from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AgentCard } from '../../components/AgentCard';
import { GridCol, GridContainer, GridRow, Section } from '../../components/ProfessionalGrid';
import { ProgressCard } from '../../components/ProgressCard';
import { TimeRemainingCard } from '../../components/TimeRemainingCard';
import { BorderRadius, Colors, Shadows, Spacing } from '../../constants/theme';
import { useAuth } from '../../contexts/AuthContext';



export default function HomeScreen() {
  const { user, signOut } = useAuth();

  // Mock data - in real app, this would come from database
  const savingsData = {
    currentAmount: 2500000,
    targetAmount: 10000000,
    selectedAgent: 'Ibu <PERSON><PERSON>',
    monthsRemaining: 8
  };

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(auth)/login');
  };

  const handleProgressCardPress = () => {
    // Navigate to detailed progress view
    console.log('Progress card pressed');
  };

  const handleAgentPress = () => {
    // Navigate to agent contact
    console.log('Agent card pressed');
  };



  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Professional Header with Islamic Branding */}
        <LinearGradient
          colors={[Colors.primary, Colors.primaryDark]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <View style={styles.headerContent}>
              <View style={styles.brandingSection}>
                <View style={styles.logoContainer}>
                  <Text style={styles.logoIcon}>🕌</Text>
                </View>
                <View style={styles.brandingText}>
                  <Text style={styles.appTitle}>Tabungan Berkah</Text>
                  <Text style={styles.headerSubtitle}>Tabungan Haji Terpercaya</Text>
                  <View style={styles.islamicAccent}>
                    <Text style={styles.arabicText}>بِسْمِ اللَّهِ</Text>
                  </View>
                </View>
              </View>
              <View style={styles.headerActions}>
                <TouchableOpacity style={styles.notificationButton}>
                  <Ionicons name="notifications-outline" size={20} color="#fff" />
                </TouchableOpacity>
                <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
                  <Ionicons name="log-out-outline" size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </LinearGradient>

        {/* Professional Grid Layout */}
        <GridContainer>
          {/* Welcome Section */}
          <Section spacing="md">
            <View style={styles.welcomeCard}>
              <View style={styles.welcomeContent}>
                <View style={styles.avatarSection}>
                  <View style={styles.avatarContainer}>
                    <Ionicons name="person-circle" size={44} color={Colors.primary} />
                  </View>
                </View>
                <View style={styles.welcomeInfo}>
                  <Text style={styles.welcomeText}>Selamat Datang!</Text>
                  <Text style={styles.userEmail}>{user?.email}</Text>
                  <View style={styles.statusIndicator}>
                    <View style={styles.statusDot} />
                    <Text style={styles.statusText}>Akun Aktif</Text>
                  </View>
                </View>
              </View>
            </View>
          </Section>

          {/* Progress Section */}
          <Section spacing="lg">
            <ProgressCard
              currentAmount={savingsData.currentAmount}
              targetAmount={savingsData.targetAmount}
              onPress={handleProgressCardPress}
            />
          </Section>

          {/* Professional Quick Actions */}
          <Section spacing="md">
            <Text style={styles.sectionTitle}>Aksi Cepat</Text>
            <GridRow gap={Spacing.sm}>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.8}>
                  <Ionicons name="add-circle" size={24} color={Colors.primary} />
                  <Text style={styles.quickActionText}>Setor</Text>
                </TouchableOpacity>
              </GridCol>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.8}>
                  <Ionicons name="document-text" size={24} color={Colors.primary} />
                  <Text style={styles.quickActionText}>Riwayat</Text>
                </TouchableOpacity>
              </GridCol>
              <GridCol flex={1}>
                <TouchableOpacity style={styles.quickActionCard} activeOpacity={0.8}>
                  <Ionicons name="calculator" size={24} color={Colors.primary} />
                  <Text style={styles.quickActionText}>Simulasi</Text>
                </TouchableOpacity>
              </GridCol>
            </GridRow>
          </Section>

          {/* Time Remaining Card */}
          <Section spacing="md">
            <TimeRemainingCard
              monthsRemaining={savingsData.monthsRemaining}
              targetDate="2025"
              hajjSeason="1446 H"
            />
          </Section>

          {/* Agent Section with Islamic Touch */}
          <Section spacing="lg">
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Agent Anda</Text>
              <View style={styles.islamicDecoration}>
                <Text style={styles.decorativeText}>☪</Text>
              </View>
            </View>
            <AgentCard
              agentName={savingsData.selectedAgent}
              agentTitle="Konsultan Haji Bersertifikat"
              onPress={handleAgentPress}
              onChatPress={() => console.log('Chat pressed')}
              onCallPress={() => console.log('Call pressed')}
            />
          </Section>

          {/* Islamic Motivation Section */}
          <Section spacing="md">
            <View style={styles.motivationCard}>
              <View style={styles.motivationHeader}>
                <Text style={styles.motivationIcon}>🤲</Text>
                <Text style={styles.motivationTitle}>Doa & Motivasi</Text>
              </View>
              <Text style={styles.motivationText}>
                "Dan berbekal-lah, dan sesungguhnya sebaik-baik bekal adalah takwa"
              </Text>
              <Text style={styles.motivationSource}>- Al-Baqarah: 197</Text>
            </View>
          </Section>
        </GridContainer>


      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },

  scrollContent: {
    paddingBottom: 100, // Space for navigation bar
  },

  headerGradient: {
    paddingTop: Spacing.xl,
    paddingBottom: Spacing.xl,
    paddingHorizontal: Spacing.container.horizontal,
    borderBottomLeftRadius: BorderRadius.xl,
    borderBottomRightRadius: BorderRadius.xl,
    marginBottom: Spacing.lg,
  },

  header: {
    minHeight: 80,
  },

  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 60,
  },

  brandingSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  logoContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.md,
  },

  logoIcon: {
    fontSize: 24,
  },

  brandingText: {
    flex: 1,
  },

  appTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
    letterSpacing: 0.5,
  },

  headerSubtitle: {
    fontSize: 13,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.85)',
    letterSpacing: 0.3,
    marginBottom: 4,
  },

  // Islamic Branding
  islamicAccent: {
    alignItems: 'flex-start',
  },

  arabicText: {
    fontSize: 11,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.7)',
    fontFamily: 'System', // Will use system Arabic font
    letterSpacing: 1,
  },

  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },

  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  signOutButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  welcomeCard: {
    marginHorizontal: Spacing.container.horizontal,
    marginBottom: Spacing.lg,
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.card,
    padding: Spacing.card.padding,
    ...Shadows.card,
  },

  welcomeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },

  avatarSection: {
    alignItems: 'center',
  },

  avatarContainer: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: Colors.accentLight,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.primary,
  },

  welcomeInfo: {
    flex: 1,
  },

  welcomeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 4,
  },

  userEmail: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textSecondary,
    marginBottom: 6,
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.success,
  },

  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.success,
  },

  // Professional Grid Layout
  gridContainer: {
    marginHorizontal: Spacing.container.horizontal,
    marginBottom: Spacing.lg,
  },

  gridRow: {
    flexDirection: 'row',
    gap: Spacing.layout.columnGap,
    marginBottom: Spacing.md,
  },

  timeRemainingSection: {
    flex: 1,
    alignItems: 'flex-start',
  },

  timeRemainingContainer: {
    marginHorizontal: Spacing.md,
    marginBottom: Spacing.lg,
    alignItems: 'flex-start',
  },

  agentSection: {
    marginBottom: Spacing.lg,
  },

  // Section Styling
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    letterSpacing: 0.3,
  },

  // Islamic Decorative Elements
  islamicDecoration: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  decorativeText: {
    fontSize: 16,
    color: Colors.gold,
  },

  // Islamic Motivation Card
  motivationCard: {
    backgroundColor: Colors.goldLight,
    borderRadius: BorderRadius.card,
    padding: Spacing.card.padding,
    borderLeftWidth: 4,
    borderLeftColor: Colors.gold,
    ...Shadows.card,
  },

  motivationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.sm,
  },

  motivationIcon: {
    fontSize: 20,
  },

  motivationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },

  motivationText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.textPrimary,
    lineHeight: 20,
    marginBottom: Spacing.xs,
    fontStyle: 'italic',
  },

  motivationSource: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textSecondary,
    textAlign: 'right',
  },

  // Professional Quick Actions
  quickActionCard: {
    backgroundColor: Colors.surface,
    borderRadius: BorderRadius.sm,
    padding: Spacing.md,
    alignItems: 'center',
    gap: Spacing.xs,
    borderWidth: 1,
    borderColor: Colors.accentMuted,
    minHeight: 80,
    ...Shadows.button,
  },

  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textPrimary,
    textAlign: 'center',
  },
});